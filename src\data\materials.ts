import { Material } from '@/types/game';

export const materials: Record<string, Material> = {
  // Shimmerstone materials
  'dull-shimmer': {
    id: 'dull-shimmer',
    name: '<PERSON><PERSON> Shimmer',
    description: 'A common crystalline fragment with a faint inner glow',
    rarity: 'common',
    value: 5,
    category: 'ore'
  },
  'bright-shimmer': {
    id: 'bright-shimmer',
    name: 'Bright Shimmer',
    description: 'A rare crystal that pulses with vibrant energy',
    rarity: 'rare',
    value: 25,
    category: 'ore'
  },
  'prismatic-core': {
    id: 'prismatic-core',
    name: 'Prismatic Core',
    description: 'A legendary crystal that refracts light into impossible colors',
    rarity: 'legendary',
    value: 200,
    category: 'ore'
  },

  // Thornwood materials
  'bark-strips': {
    id: 'bark-strips',
    name: 'Bark Strips',
    description: 'Tough bark from ancient thornwood trees',
    rarity: 'common',
    value: 3,
    category: 'essence'
  },
  'essence-sap': {
    id: 'essence-sap',
    name: '<PERSON>ssence Sap',
    description: 'Golden sap that flows with concentrated life force',
    rarity: 'rare',
    value: 18,
    category: 'essence'
  },
  'heartwood-crystal': {
    id: 'heartwood-crystal',
    name: 'Heartwood Crystal',
    description: 'A crystallized core from the oldest thornwood trees',
    rarity: 'legendary',
    value: 150,
    category: 'essence'
  },

  // Crystal Formation materials
  'crystal-shards': {
    id: 'crystal-shards',
    name: 'Crystal Shards',
    description: 'Sharp fragments of natural crystal formations',
    rarity: 'common',
    value: 4,
    category: 'crystal'
  },
  'focused-crystal': {
    id: 'focused-crystal',
    name: 'Focused Crystal',
    description: 'A perfectly formed crystal that amplifies energy',
    rarity: 'rare',
    value: 22,
    category: 'crystal'
  },
  'resonance-stone': {
    id: 'resonance-stone',
    name: 'Resonance Stone',
    description: 'A legendary crystal that hums with otherworldly power',
    rarity: 'legendary',
    value: 180,
    category: 'crystal'
  },

  // Ancient Ruins materials
  'stone-fragments': {
    id: 'stone-fragments',
    name: 'Stone Fragments',
    description: 'Weathered pieces of ancient architecture',
    rarity: 'common',
    value: 2,
    category: 'relic'
  },
  'inscribed-tablet': {
    id: 'inscribed-tablet',
    name: 'Inscribed Tablet',
    description: 'Stone tablet covered in mysterious runes',
    rarity: 'rare',
    value: 30,
    category: 'relic'
  },
  'power-relic': {
    id: 'power-relic',
    name: 'Power Relic',
    description: 'An ancient artifact that radiates forgotten magic',
    rarity: 'legendary',
    value: 250,
    category: 'relic'
  },

  // Crafted components
  'weapon-frame': {
    id: 'weapon-frame',
    name: 'Weapon Frame',
    description: 'A basic framework for weapon construction',
    rarity: 'common',
    value: 15,
    category: 'component'
  },
  'enhancement-core': {
    id: 'enhancement-core',
    name: 'Enhancement Core',
    description: 'A component that can enhance weapon properties',
    rarity: 'rare',
    value: 45,
    category: 'component'
  },
  'legendary-essence': {
    id: 'legendary-essence',
    name: 'Legendary Essence',
    description: 'Pure concentrated power from legendary materials',
    rarity: 'legendary',
    value: 300,
    category: 'component'
  }
};
