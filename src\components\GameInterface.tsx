'use client';

import { useEffect, useState } from 'react';
import { useGameStore } from '@/store/gameStore';
import { PlayerStats } from './PlayerStats';
import { ExtractionPanel } from './ExtractionPanel';
import { InventoryPanel } from './InventoryPanel';
import { CraftingPanel } from './CraftingPanel';
import { TutorialQuest } from './TutorialQuest';

export function GameInterface() {
  const { player, tick } = useGameStore();
  const [activeTab, setActiveTab] = useState<'extraction' | 'crafting' | 'inventory' | 'combat'>('extraction');
  const [showTutorial, setShowTutorial] = useState(true);

  // Game tick every second
  useEffect(() => {
    const interval = setInterval(() => {
      tick();
    }, 1000);

    return () => clearInterval(interval);
  }, [tick]);

  // Check if player has completed tutorial
  useEffect(() => {
    if (player.currentWeapon) {
      setShowTutorial(false);
    }
  }, [player.currentWeapon]);

  if (showTutorial) {
    return <TutorialQuest onComplete={() => setShowTutorial(false)} />;
  }

  return (
    <div className="min-h-screen bg-slate-900 text-white">
      {/* Header */}
      <header className="bg-slate-800 border-b border-slate-700 p-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <h1 className="text-2xl font-bold text-blue-400">IdleScaper</h1>
          <div className="text-sm text-slate-300">
            Welcome to Nexus City, {player.name}
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto p-4 grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Left Sidebar - Player Stats */}
        <div className="lg:col-span-1">
          <PlayerStats />
        </div>

        {/* Main Content Area */}
        <div className="lg:col-span-2">
          {/* Tab Navigation */}
          <div className="bg-slate-800 rounded-t-lg border-b border-slate-700">
            <nav className="flex space-x-1 p-1">
              {[
                { id: 'extraction', label: 'Extraction', icon: '⛏️' },
                { id: 'crafting', label: 'Synthesis', icon: '🔨' },
                { id: 'inventory', label: 'Inventory', icon: '🎒' },
                { id: 'combat', label: 'Combat', icon: '⚔️', disabled: !player.currentWeapon }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => !tab.disabled && setActiveTab(tab.id as any)}
                  disabled={tab.disabled}
                  className={`
                    flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors
                    ${activeTab === tab.id 
                      ? 'bg-blue-600 text-white' 
                      : tab.disabled 
                        ? 'text-slate-500 cursor-not-allowed'
                        : 'text-slate-300 hover:text-white hover:bg-slate-700'
                    }
                  `}
                >
                  <span>{tab.icon}</span>
                  <span>{tab.label}</span>
                  {tab.disabled && <span className="text-xs">(Locked)</span>}
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="bg-slate-800 rounded-b-lg p-6 min-h-[500px]">
            {activeTab === 'extraction' && <ExtractionPanel />}
            {activeTab === 'crafting' && <CraftingPanel />}
            {activeTab === 'inventory' && <InventoryPanel />}
            {activeTab === 'combat' && (
              <div className="text-center py-20">
                <h3 className="text-xl font-semibold mb-4">Combat System</h3>
                <p className="text-slate-400">
                  Combat features coming soon! You have a weapon equipped, so this area will be unlocked in the next update.
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Right Sidebar - Quick Actions & Info */}
        <div className="lg:col-span-1 space-y-6">
          {/* Current Weapon */}
          {player.currentWeapon && (
            <div className="bg-slate-800 rounded-lg p-4">
              <h3 className="text-lg font-semibold mb-3 text-green-400">Current Weapon</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-slate-300">Name:</span>
                  <span className="text-white">{player.currentWeapon.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-300">Type:</span>
                  <span className="text-white capitalize">{player.currentWeapon.type}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-300">Damage:</span>
                  <span className="text-white">{player.currentWeapon.damage}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-300">Speed:</span>
                  <span className="text-white">{player.currentWeapon.speed}</span>
                </div>
              </div>
            </div>
          )}

          {/* Quick Stats */}
          <div className="bg-slate-800 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-3 text-yellow-400">Resources</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-slate-300">Gold:</span>
                <span className="text-yellow-400">{player.gold.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-300">Reputation:</span>
                <span className="text-purple-400">{player.reputation}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-300">Essence Tokens:</span>
                <span className="text-cyan-400">{player.essenceTokens}</span>
              </div>
            </div>
          </div>

          {/* Active Extractions */}
          {player.activeExtractions.length > 0 && (
            <div className="bg-slate-800 rounded-lg p-4">
              <h3 className="text-lg font-semibold mb-3 text-blue-400">Active Extractions</h3>
              <div className="space-y-2">
                {player.activeExtractions.map((extraction, index) => {
                  const timeLeft = Math.max(0, extraction.endTime - Date.now());
                  const isComplete = timeLeft === 0;
                  
                  return (
                    <div key={index} className="text-sm">
                      <div className="flex justify-between items-center">
                        <span className="text-slate-300">Extraction {index + 1}</span>
                        <span className={isComplete ? 'text-green-400' : 'text-yellow-400'}>
                          {isComplete ? 'Complete!' : `${Math.ceil(timeLeft / 1000)}s`}
                        </span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Game Tips */}
          <div className="bg-slate-800 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-3 text-indigo-400">Tips</h3>
            <div className="text-sm text-slate-300 space-y-2">
              <p>• Higher skill levels increase rare material chances</p>
              <p>• Experiment with different material combinations</p>
              <p>• Each weapon type has unique advantages</p>
              <p>• Rare materials unlock powerful crafting options</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
