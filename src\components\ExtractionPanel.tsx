'use client';

import { useState } from 'react';
import { useGameStore } from '@/store/gameStore';
import { extractionSources } from '@/data/extractionSources';
import { materials } from '@/data/materials';

export function ExtractionPanel() {
  const { player, startExtraction, completeExtraction } = useGameStore();
  const [selectedArea, setSelectedArea] = useState('abandoned-quarry');
  const [lastExtractionResult, setLastExtractionResult] = useState<any>(null);

  const areas = {
    'abandoned-quarry': {
      name: 'Abandoned Quarry',
      description: 'Ancient mining site with rich shimmerstone deposits',
      sources: ['shimmerstone-vein-1', 'shimmerstone-vein-2', 'shimmerstone-vein-3']
    },
    'nexus-city-outskirts': {
      name: 'Nexus City Outskirts',
      description: 'Thornwood groves surrounding the great city',
      sources: ['thornwood-grove-1', 'thornwood-grove-2']
    },
    'crystal-caverns': {
      name: 'Crystal Caverns',
      description: 'Underground chambers filled with natural crystal formations',
      sources: ['crystal-formation-1', 'crystal-formation-2']
    },
    'forgotten-ruins': {
      name: 'Forgotten Ruins',
      description: 'Ancient structures holding secrets of the past',
      sources: ['ancient-ruins-1', 'ancient-ruins-2']
    }
  };

  const handleExtraction = (sourceId: string) => {
    const source = extractionSources[sourceId];
    if (!source) return;

    // Check if already extracting from this source
    const activeExtraction = player.activeExtractions.find(e => e.sourceId === sourceId);
    if (activeExtraction) {
      // Try to complete extraction
      const result = completeExtraction(sourceId);
      if (result) {
        setLastExtractionResult({ sourceId, result });
      }
    } else {
      // Start new extraction
      startExtraction(sourceId);
    }
  };

  const getExtractionStatus = (sourceId: string) => {
    const activeExtraction = player.activeExtractions.find(e => e.sourceId === sourceId);
    if (!activeExtraction) return null;

    const timeLeft = Math.max(0, activeExtraction.endTime - Date.now());
    const isComplete = timeLeft === 0;

    return {
      isActive: true,
      isComplete,
      timeLeft: Math.ceil(timeLeft / 1000)
    };
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'text-gray-400';
      case 'rare': return 'text-blue-400';
      case 'legendary': return 'text-purple-400';
      default: return 'text-white';
    }
  };

  const calculateChance = (baseChance: number, skillLevel: number, skillRequirement: number) => {
    if (skillLevel < skillRequirement) return 0;
    const skillBonus = Math.min(0.4, (skillLevel - skillRequirement) * 0.02);
    return Math.min(0.95, baseChance + skillBonus);
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2 text-orange-400">Material Extraction</h2>
        <p className="text-slate-300">
          Discover rare materials from various sources. Higher skill levels increase your chances of finding rare items.
        </p>
      </div>

      {/* Area Selection */}
      <div>
        <h3 className="text-lg font-semibold mb-3 text-slate-200">Select Area</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {Object.entries(areas).map(([areaId, area]) => (
            <button
              key={areaId}
              onClick={() => setSelectedArea(areaId)}
              className={`
                p-3 rounded-lg border-2 transition-all text-left
                ${selectedArea === areaId 
                  ? 'border-orange-500 bg-orange-500/10' 
                  : 'border-slate-600 bg-slate-700 hover:border-slate-500'
                }
              `}
            >
              <h4 className="font-semibold text-white">{area.name}</h4>
              <p className="text-sm text-slate-300 mt-1">{area.description}</p>
            </button>
          ))}
        </div>
      </div>

      {/* Extraction Sources */}
      <div>
        <h3 className="text-lg font-semibold mb-3 text-slate-200">
          Extraction Sources - {areas[selectedArea as keyof typeof areas].name}
        </h3>
        
        <div className="space-y-4">
          {areas[selectedArea as keyof typeof areas].sources.map(sourceId => {
            const source = extractionSources[sourceId];
            const status = getExtractionStatus(sourceId);
            const skillLevel = player.skills.extraction.level;

            return (
              <div key={sourceId} className="bg-slate-700 rounded-lg p-4">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h4 className="font-semibold text-white">{source.name}</h4>
                    <p className="text-sm text-slate-300">{source.description}</p>
                    <p className="text-xs text-slate-400 mt-1">
                      Extraction Time: {source.extractionTime}s
                    </p>
                  </div>
                  
                  <button
                    onClick={() => handleExtraction(sourceId)}
                    disabled={status?.isActive && !status?.isComplete}
                    className={`
                      px-4 py-2 rounded-lg font-medium transition-colors
                      ${status?.isComplete 
                        ? 'bg-green-600 hover:bg-green-700 text-white' 
                        : status?.isActive 
                          ? 'bg-yellow-600 text-white cursor-not-allowed'
                          : 'bg-orange-600 hover:bg-orange-700 text-white'
                      }
                    `}
                  >
                    {status?.isComplete 
                      ? 'Collect!' 
                      : status?.isActive 
                        ? `${status.timeLeft}s` 
                        : 'Extract'
                    }
                  </button>
                </div>

                {/* Possible Materials */}
                <div>
                  <h5 className="text-sm font-medium text-slate-200 mb-2">Possible Materials:</h5>
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
                    {source.materials.map(materialChance => {
                      const material = materials[materialChance.materialId];
                      const chance = calculateChance(
                        materialChance.baseChance, 
                        skillLevel, 
                        materialChance.skillRequirement
                      );
                      const canExtract = skillLevel >= materialChance.skillRequirement;

                      return (
                        <div 
                          key={materialChance.materialId}
                          className={`
                            p-2 rounded border text-xs
                            ${canExtract 
                              ? 'border-slate-600 bg-slate-800' 
                              : 'border-red-600 bg-red-900/20'
                            }
                          `}
                        >
                          <div className="flex justify-between items-center">
                            <span className={getRarityColor(material.rarity)}>
                              {material.name}
                            </span>
                            <span className="text-slate-400">
                              {canExtract ? `${(chance * 100).toFixed(1)}%` : 'Locked'}
                            </span>
                          </div>
                          {!canExtract && (
                            <div className="text-red-400 text-xs mt-1">
                              Requires Level {materialChance.skillRequirement}
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Last Extraction Result */}
      {lastExtractionResult && (
        <div className="bg-green-900/20 border border-green-600 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-green-400 mb-2">Extraction Complete!</h3>
          <div className="space-y-1">
            <p className="text-slate-300">
              Source: {extractionSources[lastExtractionResult.sourceId].name}
            </p>
            <p className="text-slate-300">
              Experience Gained: +{lastExtractionResult.result.experience} XP
            </p>
            <div>
              <p className="text-slate-300 mb-1">Materials Found:</p>
              {lastExtractionResult.result.materials.length > 0 ? (
                <ul className="list-disc list-inside text-sm text-slate-300 ml-4">
                  {lastExtractionResult.result.materials.map((mat: any, index: number) => (
                    <li key={index} className={getRarityColor(materials[mat.materialId].rarity)}>
                      {mat.quantity}x {materials[mat.materialId].name}
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-slate-400 text-sm ml-4">No materials found this time</p>
              )}
            </div>
          </div>
          <button
            onClick={() => setLastExtractionResult(null)}
            className="mt-3 px-3 py-1 bg-slate-600 hover:bg-slate-500 rounded text-sm"
          >
            Dismiss
          </button>
        </div>
      )}
    </div>
  );
}
