'use client';

import { useState, useEffect } from 'react';
import { useGameStore } from '@/store/gameStore';
import { materials } from '@/data/materials';

interface TutorialQuestProps {
  onComplete: () => void;
}

export function TutorialQuest({ onComplete }: TutorialQuestProps) {
  const { player, startExtraction, completeExtraction, craftWeapon, equipWeapon, addMaterial } = useGameStore();
  const [currentStep, setCurrentStep] = useState(0);
  const [stepCompleted, setStepCompleted] = useState(false);
  const [extractionResult, setExtractionResult] = useState<any>(null);

  const steps = [
    {
      title: "Welcome to IdleScaper!",
      description: "You've awakened in the mysterious world of IdleScaper. Your journey begins at the Abandoned Quarry outside Nexus City.",
      instruction: "Click 'Begin Adventure' to start your first extraction.",
      action: "begin"
    },
    {
      title: "Extract Shimmerstone",
      description: "The quarry contains valuable shimmerstone deposits. Extract materials from the Shallow Shimmerstone Vein.",
      instruction: "Click 'Extract' on the Shallow Shimmerstone Vein, then wait for completion.",
      action: "extract"
    },
    {
      title: "Collect Your Materials",
      description: "Your extraction is complete! Collect the materials you've discovered.",
      instruction: "Click 'Collect!' to gather your first materials.",
      action: "collect"
    },
    {
      title: "Forge Your First Weapon",
      description: "Now you have materials! Use the Synthesis system to forge your first weapon.",
      instruction: "Go to the Synthesis tab and craft a weapon using your materials.",
      action: "craft"
    },
    {
      title: "Equip Your Weapon",
      description: "Excellent! You've forged your first weapon. Equip it to unlock combat areas.",
      instruction: "Equip your newly crafted weapon to complete the tutorial.",
      action: "equip"
    },
    {
      title: "Tutorial Complete!",
      description: "Congratulations! You've mastered the basics of IdleScaper. The world awaits your exploration!",
      instruction: "Click 'Enter the World' to begin your adventure.",
      action: "complete"
    }
  ];

  const currentStepData = steps[currentStep];

  // Check for step completion
  useEffect(() => {
    switch (currentStep) {
      case 0:
        // Always ready to begin
        setStepCompleted(true);
        break;
      case 1:
        // Check if extraction is active
        const hasActiveExtraction = player.activeExtractions.some(e => e.sourceId === 'shimmerstone-vein-1');
        setStepCompleted(hasActiveExtraction);
        break;
      case 2:
        // Check if extraction is complete and ready to collect
        const extraction = player.activeExtractions.find(e => e.sourceId === 'shimmerstone-vein-1');
        if (extraction) {
          const isComplete = Date.now() >= extraction.endTime;
          setStepCompleted(isComplete);
        }
        break;
      case 3:
        // Check if player has materials
        const hasMaterials = Object.keys(player.inventory).length > 0;
        setStepCompleted(hasMaterials);
        break;
      case 4:
        // Check if player has crafted a weapon (has materials but no weapon yet)
        const hasWeapon = !!player.currentWeapon;
        setStepCompleted(hasWeapon);
        break;
      case 5:
        // Tutorial complete
        setStepCompleted(true);
        break;
    }
  }, [currentStep, player.activeExtractions, player.inventory, player.currentWeapon]);

  const handleStepAction = () => {
    switch (currentStepData.action) {
      case 'begin':
        // Give player some starting materials for tutorial
        addMaterial('dull-shimmer', 2);
        addMaterial('bright-shimmer', 1);
        setCurrentStep(1);
        break;
      case 'extract':
        startExtraction('shimmerstone-vein-1');
        setCurrentStep(2);
        break;
      case 'collect':
        const result = completeExtraction('shimmerstone-vein-1');
        if (result) {
          setExtractionResult(result);
          setCurrentStep(3);
        }
        break;
      case 'craft':
        // Auto-craft a weapon with available materials
        const availableMaterials = Object.keys(player.inventory).filter(id => 
          player.inventory[id] > 0
        );
        if (availableMaterials.length > 0) {
          const materialsToUse = availableMaterials.slice(0, 2); // Use first 2 materials
          const craftResult = craftWeapon(materialsToUse);
          if (craftResult.success) {
            setCurrentStep(4);
          }
        }
        break;
      case 'equip':
        // Create and equip a basic weapon
        const weapon = {
          id: 'tutorial-weapon',
          name: 'First Blade',
          type: 'melee' as const,
          damage: 12,
          speed: 6,
          materials: ['dull-shimmer', 'bright-shimmer'],
          createdAt: Date.now()
        };
        equipWeapon(weapon);
        setCurrentStep(5);
        break;
      case 'complete':
        onComplete();
        break;
    }
  };

  const getStepIcon = (step: number) => {
    if (step < currentStep) return '✅';
    if (step === currentStep) return '🔄';
    return '⏳';
  };

  return (
    <div className="min-h-screen bg-slate-900 text-white flex items-center justify-center p-4">
      <div className="max-w-4xl w-full">
        {/* Tutorial Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-blue-400 mb-2">The First Blade</h1>
          <p className="text-slate-300">Tutorial Quest</p>
        </div>

        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm text-slate-400">Progress</span>
            <span className="text-sm text-slate-400">{currentStep + 1} / {steps.length}</span>
          </div>
          <div className="w-full bg-slate-700 rounded-full h-2">
            <div 
              className="bg-blue-500 h-2 rounded-full transition-all duration-500"
              style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
            />
          </div>
        </div>

        {/* Step List */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
          {steps.map((step, index) => (
            <div
              key={index}
              className={`
                p-4 rounded-lg border-2 transition-all
                ${index === currentStep 
                  ? 'border-blue-500 bg-blue-500/10' 
                  : index < currentStep
                    ? 'border-green-500 bg-green-500/10'
                    : 'border-slate-600 bg-slate-800'
                }
              `}
            >
              <div className="flex items-center space-x-3 mb-2">
                <span className="text-2xl">{getStepIcon(index)}</span>
                <h3 className="font-semibold text-white">{step.title}</h3>
              </div>
              <p className="text-sm text-slate-300">{step.description}</p>
            </div>
          ))}
        </div>

        {/* Current Step Details */}
        <div className="bg-slate-800 rounded-lg p-6 mb-6">
          <h2 className="text-2xl font-bold text-blue-400 mb-4">{currentStepData.title}</h2>
          <p className="text-slate-300 mb-4">{currentStepData.description}</p>
          <p className="text-yellow-400 mb-6">{currentStepData.instruction}</p>

          {/* Step-specific content */}
          {currentStep === 1 && (
            <div className="bg-slate-700 rounded-lg p-4 mb-4">
              <h3 className="font-semibold text-slate-200 mb-2">Shallow Shimmerstone Vein</h3>
              <p className="text-sm text-slate-300 mb-3">
                A surface vein of shimmering crystal formations. Extraction time: 3 seconds.
              </p>
              <div className="text-sm text-slate-400">
                <p>Possible materials:</p>
                <ul className="list-disc list-inside ml-4 mt-1">
                  <li className="text-gray-400">Dull Shimmer (Common) - 80% chance</li>
                  <li className="text-blue-400">Bright Shimmer (Rare) - 15% chance</li>
                  <li className="text-purple-400">Prismatic Core (Legendary) - 2% chance</li>
                </ul>
              </div>
            </div>
          )}

          {currentStep === 2 && (
            <div className="bg-slate-700 rounded-lg p-4 mb-4">
              <h3 className="font-semibold text-slate-200 mb-2">Extraction in Progress</h3>
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-400"></div>
                <span className="text-slate-300">Extracting materials...</span>
              </div>
            </div>
          )}

          {extractionResult && currentStep === 3 && (
            <div className="bg-green-900/20 border border-green-600 rounded-lg p-4 mb-4">
              <h3 className="font-semibold text-green-400 mb-2">Extraction Complete!</h3>
              <p className="text-slate-300 mb-2">Experience Gained: +{extractionResult.experience} XP</p>
              <div>
                <p className="text-slate-300 mb-1">Materials Found:</p>
                {extractionResult.materials.length > 0 ? (
                  <ul className="list-disc list-inside text-sm text-slate-300 ml-4">
                    {extractionResult.materials.map((mat: any, index: number) => (
                      <li key={index} className={
                        materials[mat.materialId].rarity === 'legendary' ? 'text-purple-400' :
                        materials[mat.materialId].rarity === 'rare' ? 'text-blue-400' :
                        'text-gray-400'
                      }>
                        {mat.quantity}x {materials[mat.materialId].name}
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-slate-400 text-sm ml-4">No materials found this time</p>
                )}
              </div>
            </div>
          )}

          {/* Action Button */}
          <button
            onClick={handleStepAction}
            disabled={!stepCompleted && currentStep !== 0}
            className={`
              w-full py-3 rounded-lg font-semibold text-lg transition-colors
              ${stepCompleted || currentStep === 0
                ? 'bg-blue-600 hover:bg-blue-700 text-white'
                : 'bg-slate-600 text-slate-400 cursor-not-allowed'
              }
            `}
          >
            {currentStep === 0 ? 'Begin Adventure' :
             currentStep === 1 ? 'Start Extraction' :
             currentStep === 2 ? (stepCompleted ? 'Collect Materials' : 'Extracting...') :
             currentStep === 3 ? 'Craft Weapon' :
             currentStep === 4 ? 'Equip Weapon' :
             'Enter the World'
            }
          </button>
        </div>

        {/* Tutorial Tips */}
        <div className="bg-slate-800 rounded-lg p-4">
          <h3 className="font-semibold text-slate-200 mb-2">💡 Tutorial Tips</h3>
          <ul className="text-sm text-slate-300 space-y-1">
            <li>• Higher skill levels increase your chances of finding rare materials</li>
            <li>• Different material combinations create weapons with different stats</li>
            <li>• Rare and legendary materials provide significant bonuses</li>
            <li>• Your first weapon unlocks combat areas and new adventures</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
