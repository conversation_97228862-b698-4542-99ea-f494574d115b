'use client';

import { useGameStore } from '@/store/gameStore';

export function PlayerStats() {
  const { player } = useGameStore();

  const getSkillColor = (skillId: string) => {
    const colors = {
      extraction: 'text-orange-400',
      synthesis: 'text-purple-400',
      resonance: 'text-cyan-400',
      commerce: 'text-yellow-400',
      exploration: 'text-green-400'
    };
    return colors[skillId as keyof typeof colors] || 'text-white';
  };

  const getSkillIcon = (skillId: string) => {
    const icons = {
      extraction: '⛏️',
      synthesis: '🔨',
      resonance: '✨',
      commerce: '💰',
      exploration: '🗺️'
    };
    return icons[skillId as keyof typeof icons] || '📊';
  };

  return (
    <div className="bg-slate-800 rounded-lg p-4">
      <h2 className="text-xl font-bold mb-4 text-blue-400">Player Stats</h2>
      
      {/* Player Info */}
      <div className="mb-6 p-3 bg-slate-700 rounded-lg">
        <h3 className="font-semibold text-lg">{player.name}</h3>
        <p className="text-slate-300 text-sm">Level {Math.max(...Object.values(player.skills).map(s => s.level))} Adventurer</p>
        <p className="text-slate-400 text-xs mt-1">Current Area: {player.currentArea.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}</p>
      </div>

      {/* Skills */}
      <div className="space-y-3">
        <h3 className="font-semibold text-slate-200 border-b border-slate-600 pb-1">Skills</h3>
        {Object.values(player.skills).map((skill) => (
          <div key={skill.id} className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className="text-lg">{getSkillIcon(skill.id)}</span>
                <span className={`font-medium ${getSkillColor(skill.id)}`}>
                  {skill.name}
                </span>
              </div>
              <span className="text-white font-semibold">
                Lv. {skill.level}
              </span>
            </div>
            
            {/* Experience Bar */}
            <div className="w-full bg-slate-700 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-300 ${
                  skill.id === 'extraction' ? 'bg-orange-500' :
                  skill.id === 'synthesis' ? 'bg-purple-500' :
                  skill.id === 'resonance' ? 'bg-cyan-500' :
                  skill.id === 'commerce' ? 'bg-yellow-500' :
                  'bg-green-500'
                }`}
                style={{ 
                  width: `${(skill.experience / skill.experienceToNext) * 100}%` 
                }}
              />
            </div>
            
            <div className="flex justify-between text-xs text-slate-400">
              <span>{skill.experience.toLocaleString()} XP</span>
              <span>{skill.experienceToNext.toLocaleString()} XP to next</span>
            </div>
          </div>
        ))}
      </div>

      {/* Inventory Summary */}
      <div className="mt-6 pt-4 border-t border-slate-600">
        <h3 className="font-semibold text-slate-200 mb-2">Inventory Summary</h3>
        <div className="text-sm text-slate-300">
          <div className="flex justify-between">
            <span>Total Items:</span>
            <span>{Object.values(player.inventory).reduce((sum, qty) => sum + qty, 0)}</span>
          </div>
          <div className="flex justify-between">
            <span>Unique Materials:</span>
            <span>{Object.keys(player.inventory).length}</span>
          </div>
        </div>
      </div>

      {/* Achievements/Progress */}
      <div className="mt-4 pt-4 border-t border-slate-600">
        <h3 className="font-semibold text-slate-200 mb-2">Progress</h3>
        <div className="space-y-1 text-sm">
          <div className="flex items-center space-x-2">
            <span className={player.currentWeapon ? 'text-green-400' : 'text-slate-500'}>
              {player.currentWeapon ? '✅' : '⏳'}
            </span>
            <span className={player.currentWeapon ? 'text-green-400' : 'text-slate-400'}>
              First Weapon Crafted
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <span className={player.discoveredRecipes.length > 0 ? 'text-green-400' : 'text-slate-500'}>
              {player.discoveredRecipes.length > 0 ? '✅' : '⏳'}
            </span>
            <span className={player.discoveredRecipes.length > 0 ? 'text-green-400' : 'text-slate-400'}>
              Recipe Discovery
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <span className={player.unlockedAreas.length > 2 ? 'text-green-400' : 'text-slate-500'}>
              {player.unlockedAreas.length > 2 ? '✅' : '⏳'}
            </span>
            <span className={player.unlockedAreas.length > 2 ? 'text-green-400' : 'text-slate-400'}>
              Area Explorer
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
