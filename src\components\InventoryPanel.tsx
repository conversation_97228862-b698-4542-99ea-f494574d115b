'use client';

import { useState } from 'react';
import { useGameStore } from '@/store/gameStore';
import { materials } from '@/data/materials';

export function InventoryPanel() {
  const { player } = useGameStore();
  const [filter, setFilter] = useState<'all' | 'ore' | 'essence' | 'crystal' | 'relic' | 'component'>('all');
  const [sortBy, setSortBy] = useState<'name' | 'quantity' | 'value' | 'rarity'>('name');

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'text-gray-400 border-gray-600';
      case 'rare': return 'text-blue-400 border-blue-600';
      case 'legendary': return 'text-purple-400 border-purple-600';
      default: return 'text-white border-slate-600';
    }
  };

  const getRarityBg = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'bg-gray-900/20';
      case 'rare': return 'bg-blue-900/20';
      case 'legendary': return 'bg-purple-900/20';
      default: return 'bg-slate-800';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'ore': return '⛏️';
      case 'essence': return '🌿';
      case 'crystal': return '💎';
      case 'relic': return '🏺';
      case 'component': return '⚙️';
      default: return '📦';
    }
  };

  // Get inventory items with material data
  const inventoryItems = Object.entries(player.inventory)
    .map(([materialId, quantity]) => ({
      materialId,
      quantity,
      material: materials[materialId]
    }))
    .filter(item => item.material && quantity > 0)
    .filter(item => filter === 'all' || item.material.category === filter);

  // Sort items
  const sortedItems = [...inventoryItems].sort((a, b) => {
    switch (sortBy) {
      case 'name':
        return a.material.name.localeCompare(b.material.name);
      case 'quantity':
        return b.quantity - a.quantity;
      case 'value':
        return (b.material.value * b.quantity) - (a.material.value * a.quantity);
      case 'rarity':
        const rarityOrder = { 'legendary': 3, 'rare': 2, 'common': 1 };
        return (rarityOrder[b.material.rarity as keyof typeof rarityOrder] || 0) - 
               (rarityOrder[a.material.rarity as keyof typeof rarityOrder] || 0);
      default:
        return 0;
    }
  });

  const totalValue = inventoryItems.reduce((sum, item) => 
    sum + (item.material.value * item.quantity), 0
  );

  const categoryStats = {
    ore: inventoryItems.filter(item => item.material.category === 'ore').length,
    essence: inventoryItems.filter(item => item.material.category === 'essence').length,
    crystal: inventoryItems.filter(item => item.material.category === 'crystal').length,
    relic: inventoryItems.filter(item => item.material.category === 'relic').length,
    component: inventoryItems.filter(item => item.material.category === 'component').length,
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2 text-green-400">Inventory</h2>
        <div className="flex flex-wrap gap-4 text-sm text-slate-300">
          <span>Total Items: {inventoryItems.reduce((sum, item) => sum + item.quantity, 0)}</span>
          <span>Unique Materials: {inventoryItems.length}</span>
          <span>Total Value: {totalValue.toLocaleString()} gold</span>
        </div>
      </div>

      {/* Filters and Sorting */}
      <div className="flex flex-wrap gap-4 items-center">
        <div>
          <label className="text-sm text-slate-300 mr-2">Filter:</label>
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value as any)}
            className="bg-slate-700 border border-slate-600 rounded px-3 py-1 text-white"
          >
            <option value="all">All ({inventoryItems.length})</option>
            <option value="ore">Ore ({categoryStats.ore})</option>
            <option value="essence">Essence ({categoryStats.essence})</option>
            <option value="crystal">Crystal ({categoryStats.crystal})</option>
            <option value="relic">Relic ({categoryStats.relic})</option>
            <option value="component">Component ({categoryStats.component})</option>
          </select>
        </div>

        <div>
          <label className="text-sm text-slate-300 mr-2">Sort by:</label>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="bg-slate-700 border border-slate-600 rounded px-3 py-1 text-white"
          >
            <option value="name">Name</option>
            <option value="quantity">Quantity</option>
            <option value="value">Total Value</option>
            <option value="rarity">Rarity</option>
          </select>
        </div>
      </div>

      {/* Inventory Grid */}
      {sortedItems.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {sortedItems.map(({ materialId, quantity, material }) => (
            <div
              key={materialId}
              className={`
                p-4 rounded-lg border-2 transition-all
                ${getRarityBg(material.rarity)} ${getRarityColor(material.rarity)}
              `}
            >
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <span className="text-xl">{getCategoryIcon(material.category)}</span>
                  <div>
                    <h3 className="font-semibold text-white">{material.name}</h3>
                    <p className="text-xs text-slate-400 capitalize">{material.category}</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold text-white">{quantity}</div>
                  <div className="text-xs text-slate-400">
                    {(material.value * quantity).toLocaleString()}g
                  </div>
                </div>
              </div>

              <p className="text-sm text-slate-300 mb-3">{material.description}</p>

              <div className="flex justify-between items-center text-xs">
                <span className={`
                  px-2 py-1 rounded-full font-medium
                  ${material.rarity === 'legendary' ? 'bg-purple-600/20 text-purple-300' :
                    material.rarity === 'rare' ? 'bg-blue-600/20 text-blue-300' :
                    'bg-gray-600/20 text-gray-300'
                  }
                `}>
                  {material.rarity.charAt(0).toUpperCase() + material.rarity.slice(1)}
                </span>
                <span className="text-slate-400">
                  {material.value}g each
                </span>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">📦</div>
          <h3 className="text-xl font-semibold text-slate-300 mb-2">
            {filter === 'all' ? 'No Items Found' : `No ${filter.charAt(0).toUpperCase() + filter.slice(1)} Items`}
          </h3>
          <p className="text-slate-400">
            {filter === 'all' 
              ? 'Start extracting materials to build your inventory!'
              : `Try extracting materials or change your filter to see other items.`
            }
          </p>
        </div>
      )}

      {/* Quick Stats */}
      {inventoryItems.length > 0 && (
        <div className="bg-slate-700 rounded-lg p-4">
          <h3 className="font-semibold text-slate-200 mb-3">Inventory Statistics</h3>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-center">
            {Object.entries(categoryStats).map(([category, count]) => (
              <div key={category} className="space-y-1">
                <div className="text-2xl">{getCategoryIcon(category)}</div>
                <div className="text-lg font-semibold text-white">{count}</div>
                <div className="text-xs text-slate-400 capitalize">{category}</div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
