// Core game types and interfaces

export interface Material {
  id: string;
  name: string;
  description: string;
  rarity: 'common' | 'rare' | 'legendary';
  value: number;
  category: 'ore' | 'essence' | 'crystal' | 'relic' | 'component';
}

export interface ExtractionSource {
  id: string;
  name: string;
  description: string;
  location: string;
  materials: {
    materialId: string;
    baseChance: number;
    skillRequirement: number;
  }[];
  extractionTime: number; // in seconds
}

export interface Skill {
  id: string;
  name: string;
  level: number;
  experience: number;
  experienceToNext: number;
}

export interface Recipe {
  id: string;
  name: string;
  description: string;
  inputs: { materialId: string; quantity: number }[];
  outputs: { materialId: string; quantity: number; chance: number }[];
  discoveredBy?: string[]; // player IDs who discovered this recipe
}

export interface Weapon {
  id: string;
  name: string;
  type: 'melee' | 'ranged' | 'magic';
  damage: number;
  speed: number;
  special?: string;
  materials: string[]; // materials used to create it
  createdAt: number;
}

export interface Monster {
  id: string;
  name: string;
  description: string;
  health: number;
  damage: number;
  defense: number;
  speed: number;
  loot: {
    materialId: string;
    quantity: number;
    chance: number;
  }[];
  weakTo?: 'melee' | 'ranged' | 'magic';
}

export interface Area {
  id: string;
  name: string;
  description: string;
  unlockRequirement?: {
    type: 'weapon' | 'material' | 'skill';
    requirement: string | { skillId: string; level: number };
  };
  extractionSources: string[];
  monsters: string[];
}

export interface PlayerState {
  id: string;
  name: string;
  currentArea: string;
  skills: Record<string, Skill>;
  inventory: Record<string, number>;
  gold: number;
  reputation: number;
  essenceTokens: number;
  currentWeapon?: Weapon;
  discoveredRecipes: string[];
  unlockedAreas: string[];
  activeExtractions: {
    sourceId: string;
    startTime: number;
    endTime: number;
  }[];
  questProgress: Record<string, any>;
}

export interface GameState {
  player: PlayerState;
  currentTime: number;
  marketPrices: Record<string, number>;
  globalDiscoveries: string[]; // recipes discovered by any player
}

export interface Quest {
  id: string;
  name: string;
  description: string;
  objectives: {
    id: string;
    description: string;
    type: 'extract' | 'craft' | 'combat' | 'discover';
    target: string;
    quantity?: number;
    completed: boolean;
  }[];
  rewards: {
    type: 'gold' | 'experience' | 'material' | 'unlock';
    value: string | number;
    skillId?: string;
  }[];
  completed: boolean;
}

// Utility types for calculations
export interface ExtractionResult {
  materials: { materialId: string; quantity: number }[];
  experience: number;
  time: number;
}

export interface CraftingResult {
  success: boolean;
  outputs: { materialId: string; quantity: number }[];
  newRecipeDiscovered?: Recipe;
  experience: number;
}

export interface CombatResult {
  victory: boolean;
  damage: number;
  loot: { materialId: string; quantity: number }[];
  experience: number;
}
