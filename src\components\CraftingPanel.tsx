'use client';

import { useState } from 'react';
import { useGameStore } from '@/store/gameStore';
import { materials } from '@/data/materials';

export function CraftingPanel() {
  const { player, craftWeapon, equipWeapon } = useGameStore();
  const [selectedMaterials, setSelectedMaterials] = useState<string[]>([]);
  const [lastCraftResult, setLastCraftResult] = useState<any>(null);

  const availableMaterials = Object.entries(player.inventory)
    .map(([materialId, quantity]) => ({
      materialId,
      quantity,
      material: materials[materialId]
    }))
    .filter(item => item.material && item.quantity > 0);

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'text-gray-400';
      case 'rare': return 'text-blue-400';
      case 'legendary': return 'text-purple-400';
      default: return 'text-white';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'ore': return '⛏️';
      case 'essence': return '🌿';
      case 'crystal': return '💎';
      case 'relic': return '🏺';
      case 'component': return '⚙️';
      default: return '📦';
    }
  };

  const toggleMaterial = (materialId: string) => {
    setSelectedMaterials(prev => {
      if (prev.includes(materialId)) {
        return prev.filter(id => id !== materialId);
      } else if (prev.length < 4) { // Max 4 materials per craft
        return [...prev, materialId];
      }
      return prev;
    });
  };

  const predictWeaponStats = () => {
    if (selectedMaterials.length === 0) return null;

    let totalValue = 0;
    let hasRare = false;
    let hasLegendary = false;
    
    selectedMaterials.forEach(id => {
      const material = materials[id];
      if (material) {
        totalValue += material.value;
        if (material.rarity === 'rare') hasRare = true;
        if (material.rarity === 'legendary') hasLegendary = true;
      }
    });

    let damage = Math.floor(totalValue / 10) + 5;
    let speed = 5;
    
    if (hasRare) {
      damage += 3;
      speed += 1;
    }
    if (hasLegendary) {
      damage += 8;
      speed += 2;
    }

    return { damage: damage + 1, speed: speed + 1 }; // +1 for type bonus average
  };

  const handleCraft = () => {
    if (selectedMaterials.length === 0) return;

    const result = craftWeapon(selectedMaterials);
    setLastCraftResult(result);
    
    if (result.success) {
      setSelectedMaterials([]);
    }
  };

  const canCraft = selectedMaterials.length > 0 && 
    selectedMaterials.every(materialId => 
      (player.inventory[materialId] || 0) >= 1
    );

  const predictedStats = predictWeaponStats();

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2 text-purple-400">Weapon Synthesis</h2>
        <p className="text-slate-300">
          Combine materials to create unique weapons. Different combinations yield different weapon types and stats.
        </p>
      </div>

      {/* Crafting Interface */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Material Selection */}
        <div>
          <h3 className="text-lg font-semibold mb-3 text-slate-200">Available Materials</h3>
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {availableMaterials.map(({ materialId, quantity, material }) => (
              <button
                key={materialId}
                onClick={() => toggleMaterial(materialId)}
                disabled={selectedMaterials.includes(materialId) ? false : selectedMaterials.length >= 4}
                className={`
                  w-full p-3 rounded-lg border-2 text-left transition-all
                  ${selectedMaterials.includes(materialId)
                    ? 'border-purple-500 bg-purple-500/10'
                    : selectedMaterials.length >= 4
                      ? 'border-slate-600 bg-slate-700 opacity-50 cursor-not-allowed'
                      : 'border-slate-600 bg-slate-700 hover:border-slate-500'
                  }
                `}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <span className="text-xl">{getCategoryIcon(material.category)}</span>
                    <div>
                      <h4 className={`font-semibold ${getRarityColor(material.rarity)}`}>
                        {material.name}
                      </h4>
                      <p className="text-xs text-slate-400 capitalize">
                        {material.category} • {material.rarity}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-white font-semibold">{quantity}</div>
                    <div className="text-xs text-slate-400">{material.value}g</div>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Crafting Preview */}
        <div>
          <h3 className="text-lg font-semibold mb-3 text-slate-200">Weapon Preview</h3>
          <div className="bg-slate-700 rounded-lg p-4 space-y-4">
            {/* Selected Materials */}
            <div>
              <h4 className="font-medium text-slate-200 mb-2">
                Selected Materials ({selectedMaterials.length}/4)
              </h4>
              {selectedMaterials.length > 0 ? (
                <div className="space-y-2">
                  {selectedMaterials.map(materialId => {
                    const material = materials[materialId];
                    return (
                      <div key={materialId} className="flex items-center justify-between bg-slate-800 rounded p-2">
                        <div className="flex items-center space-x-2">
                          <span>{getCategoryIcon(material.category)}</span>
                          <span className={`text-sm ${getRarityColor(material.rarity)}`}>
                            {material.name}
                          </span>
                        </div>
                        <button
                          onClick={() => toggleMaterial(materialId)}
                          className="text-red-400 hover:text-red-300 text-sm"
                        >
                          Remove
                        </button>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <p className="text-slate-400 text-sm">No materials selected</p>
              )}
            </div>

            {/* Predicted Stats */}
            {predictedStats && (
              <div>
                <h4 className="font-medium text-slate-200 mb-2">Predicted Stats</h4>
                <div className="bg-slate-800 rounded p-3 space-y-2">
                  <div className="flex justify-between">
                    <span className="text-slate-300">Weapon Type:</span>
                    <span className="text-white">Random (Melee/Ranged/Magic)</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-300">Damage:</span>
                    <span className="text-white">{predictedStats.damage - 1} - {predictedStats.damage + 1}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-300">Speed:</span>
                    <span className="text-white">{predictedStats.speed - 1} - {predictedStats.speed + 1}</span>
                  </div>
                </div>
              </div>
            )}

            {/* Craft Button */}
            <button
              onClick={handleCraft}
              disabled={!canCraft}
              className={`
                w-full py-3 rounded-lg font-semibold transition-colors
                ${canCraft
                  ? 'bg-purple-600 hover:bg-purple-700 text-white'
                  : 'bg-slate-600 text-slate-400 cursor-not-allowed'
                }
              `}
            >
              {selectedMaterials.length === 0 
                ? 'Select Materials to Craft'
                : canCraft
                  ? 'Forge Weapon'
                  : 'Insufficient Materials'
              }
            </button>
          </div>
        </div>
      </div>

      {/* Craft Result */}
      {lastCraftResult && (
        <div className={`
          border rounded-lg p-4
          ${lastCraftResult.success 
            ? 'bg-green-900/20 border-green-600' 
            : 'bg-red-900/20 border-red-600'
          }
        `}>
          <h3 className={`text-lg font-semibold mb-2 ${
            lastCraftResult.success ? 'text-green-400' : 'text-red-400'
          }`}>
            {lastCraftResult.success ? 'Weapon Forged Successfully!' : 'Crafting Failed'}
          </h3>
          
          {lastCraftResult.success ? (
            <div className="space-y-2">
              <p className="text-slate-300">
                Experience Gained: +{lastCraftResult.experience} Synthesis XP
              </p>
              <p className="text-slate-300">
                A new weapon has been created! Check your inventory to see the results.
              </p>
              {!player.currentWeapon && (
                <div className="mt-3">
                  <p className="text-yellow-400 mb-2">
                    This is your first weapon! Would you like to equip it?
                  </p>
                  <button
                    onClick={() => {
                      // This is a simplified version - in a real implementation,
                      // we'd need to properly handle the weapon object
                      const weaponStats = predictWeaponStats();
                      if (weaponStats) {
                        const weapon = {
                          id: `weapon-${Date.now()}`,
                          name: 'Crafted Weapon',
                          type: 'melee' as const,
                          damage: weaponStats.damage,
                          speed: weaponStats.speed,
                          materials: selectedMaterials,
                          createdAt: Date.now()
                        };
                        equipWeapon(weapon);
                      }
                    }}
                    className="px-4 py-2 bg-green-600 hover:bg-green-700 rounded text-white font-medium"
                  >
                    Equip Weapon
                  </button>
                </div>
              )}
            </div>
          ) : (
            <p className="text-slate-300">
              Something went wrong during crafting. Please try again.
            </p>
          )}
          
          <button
            onClick={() => setLastCraftResult(null)}
            className="mt-3 px-3 py-1 bg-slate-600 hover:bg-slate-500 rounded text-sm"
          >
            Dismiss
          </button>
        </div>
      )}

      {/* Crafting Tips */}
      <div className="bg-slate-700 rounded-lg p-4">
        <h3 className="font-semibold text-slate-200 mb-2">Synthesis Tips</h3>
        <ul className="text-sm text-slate-300 space-y-1">
          <li>• Higher value materials create stronger weapons</li>
          <li>• Rare materials add significant stat bonuses</li>
          <li>• Legendary materials provide the biggest improvements</li>
          <li>• Weapon type (Melee/Ranged/Magic) is randomly determined</li>
          <li>• You can use 1-4 materials per weapon</li>
        </ul>
      </div>
    </div>
  );
}
