import { ExtractionSource } from '@/types/game';

export const extractionSources: Record<string, ExtractionSource> = {
  'shimmerstone-vein-1': {
    id: 'shimmerstone-vein-1',
    name: 'Shallow Shimmerstone Vein',
    description: 'A surface vein of shimmering crystal formations',
    location: 'Abandoned Quarry',
    materials: [
      { materialId: 'dull-shimmer', baseChance: 0.8, skillRequirement: 1 },
      { materialId: 'bright-shimmer', baseChance: 0.15, skillRequirement: 5 },
      { materialId: 'prismatic-core', baseChance: 0.02, skillRequirement: 20 }
    ],
    extractionTime: 3
  },
  'shimmerstone-vein-2': {
    id: 'shimmerstone-vein-2',
    name: 'Deep Shimmerstone Vein',
    description: 'A rich vein deep within the quarry walls',
    location: 'Abandoned Quarry',
    materials: [
      { materialId: 'dull-shimmer', baseChance: 0.6, skillRequirement: 1 },
      { materialId: 'bright-shimmer', baseChance: 0.3, skillRequirement: 3 },
      { materialId: 'prismatic-core', baseChance: 0.05, skillRequirement: 15 }
    ],
    extractionTime: 4
  },
  'shimmerstone-vein-3': {
    id: 'shimmerstone-vein-3',
    name: 'Ancient Shimmerstone Deposit',
    description: 'An ancient formation with concentrated crystal energy',
    location: 'Abandoned Quarry',
    materials: [
      { materialId: 'dull-shimmer', baseChance: 0.4, skillRequirement: 1 },
      { materialId: 'bright-shimmer', baseChance: 0.4, skillRequirement: 1 },
      { materialId: 'prismatic-core', baseChance: 0.1, skillRequirement: 10 }
    ],
    extractionTime: 5
  },

  'thornwood-grove-1': {
    id: 'thornwood-grove-1',
    name: 'Young Thornwood Grove',
    description: 'A cluster of younger thornwood trees',
    location: 'Nexus City Outskirts',
    materials: [
      { materialId: 'bark-strips', baseChance: 0.85, skillRequirement: 1 },
      { materialId: 'essence-sap', baseChance: 0.12, skillRequirement: 8 },
      { materialId: 'heartwood-crystal', baseChance: 0.01, skillRequirement: 25 }
    ],
    extractionTime: 2
  },
  'thornwood-grove-2': {
    id: 'thornwood-grove-2',
    name: 'Ancient Thornwood Stand',
    description: 'Massive thornwood trees that have stood for centuries',
    location: 'Nexus City Outskirts',
    materials: [
      { materialId: 'bark-strips', baseChance: 0.5, skillRequirement: 1 },
      { materialId: 'essence-sap', baseChance: 0.35, skillRequirement: 5 },
      { materialId: 'heartwood-crystal', baseChance: 0.08, skillRequirement: 18 }
    ],
    extractionTime: 6
  },

  'crystal-formation-1': {
    id: 'crystal-formation-1',
    name: 'Surface Crystal Cluster',
    description: 'Natural crystal formations exposed to the elements',
    location: 'Crystal Caverns',
    materials: [
      { materialId: 'crystal-shards', baseChance: 0.9, skillRequirement: 1 },
      { materialId: 'focused-crystal', baseChance: 0.08, skillRequirement: 12 },
      { materialId: 'resonance-stone', baseChance: 0.005, skillRequirement: 30 }
    ],
    extractionTime: 2
  },
  'crystal-formation-2': {
    id: 'crystal-formation-2',
    name: 'Deep Crystal Chamber',
    description: 'A hidden chamber filled with pristine crystal formations',
    location: 'Crystal Caverns',
    materials: [
      { materialId: 'crystal-shards', baseChance: 0.4, skillRequirement: 1 },
      { materialId: 'focused-crystal', baseChance: 0.45, skillRequirement: 8 },
      { materialId: 'resonance-stone', baseChance: 0.06, skillRequirement: 22 }
    ],
    extractionTime: 7
  },

  'ancient-ruins-1': {
    id: 'ancient-ruins-1',
    name: 'Crumbling Wall',
    description: 'The remains of an ancient stone wall',
    location: 'Forgotten Ruins',
    materials: [
      { materialId: 'stone-fragments', baseChance: 0.95, skillRequirement: 1 },
      { materialId: 'inscribed-tablet', baseChance: 0.04, skillRequirement: 15 },
      { materialId: 'power-relic', baseChance: 0.002, skillRequirement: 35 }
    ],
    extractionTime: 1
  },
  'ancient-ruins-2': {
    id: 'ancient-ruins-2',
    name: 'Sacred Altar',
    description: 'An ancient altar still humming with residual power',
    location: 'Forgotten Ruins',
    materials: [
      { materialId: 'stone-fragments', baseChance: 0.3, skillRequirement: 1 },
      { materialId: 'inscribed-tablet', baseChance: 0.5, skillRequirement: 10 },
      { materialId: 'power-relic', baseChance: 0.15, skillRequirement: 25 }
    ],
    extractionTime: 8
  }
};
