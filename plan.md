# IdleScaper - Revolutionary Idle Adventure
## Core Innovation Philosophy

**IdleScaper** reimagines idle gaming by replacing linear progression with **discovery-driven advancement**. Instead of grinding bronze→iron→steel, players hunt for rare materials that unlock entirely new possibilities. Every skill interaction creates potential for unexpected discoveries.

## Revolutionary Game Loop

### The Discovery Cycle
1. **Explore & Extract**: Mine mysterious veins, harvest unknown flora, investigate ruins
2. **Analyze & Experiment**: Combine materials to discover new recipes and possibilities  
3. **Craft & Unlock**: Create unique items that open new areas, skills, or mechanics
4. **Venture Forward**: Use crafted items to access previously impossible content

### Skill-Based RNG System
- **Low Skill (1-10)**: 5-15% chance for rare discoveries, slow extraction
- **Medium Skill (11-25)**: 25-40% chance, moderate speed, unlock material variants
- **High Skill (26-50)**: 60-80% chance, fast extraction, guaranteed rares from basic sources
- **Master Skill (51+)**: 90%+ chance, ultra-fast, access to legendary materials

## Core Skills System

### 1. **Extraction** (Mining/Harvesting)
**Unique Materials System:**
- **<PERSON><PERSON><PERSON> Veins**: Yield common Dull Shimmer, rare Bright Shimmer, legendary Prismatic Core
- **Thornwood Groves**: Common Bark Strips, rare Essence Sap, legendary Heartwood Crystal  
- **Crystal Formations**: Common Shards, rare Focused Crystals, legendary Resonance Stones
- **Ancient Ruins**: Common Stone Fragments, rare Inscribed Tablets, legendary Power Relics

**Innovation**: Each source has 3-5 possible outcomes with dramatically different uses

### 2. **Synthesis** (Crafting/Alchemy)
**Discovery-Based Recipes:**
- **Weapon Forging**: Combine any 2+ rare materials to create randomized weapons
- **Enhancement Brewing**: Mix materials to create temporary or permanent buffs
- **Tool Creation**: Craft specialized extraction tools for specific material types
- **Essence Binding**: Fuse materials to create entirely new hybrid resources

**Innovation**: No fixed recipes - experimentation reveals new combinations

### 3. **Resonance** (Magic/Enchanting)
**Energy Manipulation:**
- **Material Attunement**: Enhance extraction chances for specific material types
- **Weapon Awakening**: Unlock hidden properties in crafted weapons
- **Area Sensing**: Detect rare material locations before extraction
- **Temporal Acceleration**: Speed up idle progression for short periods

### 4. **Commerce** (Trading/Economics)
**Dynamic Market System:**
- **Supply & Demand**: Prices fluctuate based on player actions and discoveries
- **Trade Routes**: Establish connections between different areas for profit
- **Merchant Contracts**: Long-term deals for steady income streams
- **Market Manipulation**: Buy low, create artificial scarcity, sell high

### 5. **Exploration** (Adventure/Discovery)
**Progressive Area Unlocking:**
- **Scouting**: Discover new extraction sites and hidden areas
- **Pathfinding**: Create efficient routes between locations
- **Danger Assessment**: Evaluate combat requirements for new areas
- **Lore Discovery**: Uncover world history that provides gameplay bonuses

## Tutorial Quest: "The First Blade"

### Phase 1: Discovery (5 minutes)
**Location**: Abandoned Quarry outside Nexus City
- **Objective**: Extract from 3 different Shimmerstone veins
- **Learning**: Basic extraction, RNG system, material identification
- **Outcome**: Collect Dull Shimmer (common), hopefully 1 Bright Shimmer (rare)

### Phase 2: Experimentation (5 minutes)  
**Location**: City Synthesis Workshop
- **Objective**: Attempt weapon forging with collected materials
- **Learning**: Discovery-based crafting, material combination effects
- **Outcome**: Create first weapon (stats/type depend on materials used)

### Phase 3: Awakening (3 minutes)
**Location**: Resonance Chamber
- **Objective**: Attune to weapon, unlock basic combat abilities
- **Learning**: Resonance system, weapon enhancement
- **Outcome**: Weapon gains special properties, combat unlocked

### Phase 4: First Venture (7 minutes)
**Location**: Whispering Caverns (First Dungeon)
- **Objective**: Defeat 3 different monster types, extract rare materials
- **Learning**: Combat mechanics, dungeon rewards, skill synergy
- **Outcome**: Access to advanced areas, new material types discovered

## Nexus City - The Hub

### Districts & Functions
**Synthesis Quarter**: Crafting workshops, experimentation labs, recipe libraries
**Commerce District**: Dynamic marketplace, merchant guilds, trade route planning
**Resonance Sanctum**: Enchanting facilities, magical research, temporal chambers  
**Explorer's Lodge**: Area maps, scouting reports, expedition planning
**The Foundries**: Specialized weapon/tool creation, material processing

### Key NPCs
**Master Synthesist Kira**: Teaches advanced crafting, sells rare catalysts
**Merchant Prince Valdris**: Market information, trade route contracts
**Resonance Keeper Zara**: Magical enhancement services, temporal acceleration
**Scout Captain Thorne**: Area unlocks, danger assessments, exploration contracts

## First Dungeon: Whispering Caverns

### Monster Ecosystem (5 Types)
1. **Shimmer Sprites**: Fast, low HP, drop Concentrated Shimmer
2. **Stone Guardians**: Slow, high HP, drop Ancient Fragments  
3. **Echo Wraiths**: Medium stats, drop Spectral Essence
4. **Crystal Crawlers**: Defensive, drop Refined Crystal Shards
5. **Cavern Tyrant** (Boss): High stats, drops Legendary Core Fragment

### Unique Mechanics
- **Resonance Interactions**: Different weapon types have advantages vs specific monsters
- **Environmental Extraction**: Mine special nodes only accessible during combat
- **Discovery Rewards**: First-time kills unlock new synthesis recipes
- **Scaling Difficulty**: Monster strength adapts to player weapon quality

## Economy & Progression

### Currency System
**Primary**: Gold Coins (from selling common materials, quest rewards)
**Secondary**: Reputation Points (earned through trade, exploration, discovery)
**Premium**: Essence Tokens (rare drops, used for high-end services)

### Progression Gates
- **Combat Access**: Requires any crafted weapon
- **Advanced Areas**: Require specific material discoveries or weapon types
- **Master Services**: Require reputation thresholds with specific NPCs
- **Legendary Content**: Requires multiple rare material discoveries

## Technical Architecture

### Modular System Design
```
/src
  /core          # Game engine, save system, calculations
  /skills        # Individual skill modules (easily expandable)
  /areas         # Location definitions and mechanics
  /items         # Material and equipment systems
  /ui            # Reusable interface components
  /data          # Game balance, recipes, monster stats
```

### Expandability Framework
- **Skill Plugins**: New skills integrate via standardized interfaces
- **Area Templates**: Consistent structure for adding new locations
- **Material System**: Automatic integration of new materials into existing recipes
- **Monster Framework**: Scalable combat system for unlimited monster types

## Demo Scope (3-4 weeks)

### Week 1: Core Systems
- [ ] Material extraction with RNG system
- [ ] Discovery-based synthesis system
- [ ] Basic weapon creation and stats
- [ ] Nexus City hub with essential NPCs

### Week 2: Combat & Dungeon
- [ ] Combat system with weapon-type advantages
- [ ] Whispering Caverns with 5 monster types
- [ ] Environmental interaction during combat
- [ ] Loot and progression systems

### Week 3: Economy & Polish
- [ ] Dynamic marketplace with price fluctuations
- [ ] Trade route and contract systems
- [ ] Tutorial quest implementation
- [ ] UI polish and user experience refinement

### Week 4: Integration & Testing
- [ ] Skill synergy systems
- [ ] Save/load functionality
- [ ] Performance optimization
- [ ] Playtesting and balance adjustments

## Innovation Highlights

🎲 **True RNG Progression**: Skill level affects discovery chances, not linear unlocks
⚗️ **Experimental Crafting**: No fixed recipes - discovery through experimentation  
🗡️ **Meaningful Weapons**: Each crafted weapon is unique and opens different possibilities
🏙️ **Living Economy**: Prices and opportunities change based on player actions
🔍 **Discovery-Driven**: Progress through finding new materials and combinations
⚡ **Seamless Integration**: All skills work together naturally, no artificial barriers

This system creates genuine excitement around each extraction attempt and crafting experiment, while maintaining the idle game's core appeal of steady progression and optimization opportunities.
