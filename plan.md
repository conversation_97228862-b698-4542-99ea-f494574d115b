# IdleScaper - Modern Idle Adventure Game
## Project Vision & Core Concept

**IdleScaper** is a modern web-based idle game that blends the nostalgic progression systems of OSRS with the elegant simplicity of Melvor Idle, built on Next.js 15 with a focus on developer experience and player engagement.

### Core Philosophy
- **Nostalgic yet Fresh**: Familiar mechanics with unique twists and modern UX
- **Meaningful Progression**: Every action contributes to multiple interconnected systems
- **Active Rewards**: Idle progression enhanced by active participation bonuses
- **Clean & Elegant**: Beautiful, minimalist UI that doesn't overwhelm
- **Developer-Friendly**: Built with modern tools for rapid iteration

## Game Loop & Core Mechanics

### Primary Loop (5-10 minutes)
1. **Check Progress**: Review idle gains across all active skills
2. **Optimize Setup**: Adjust equipment, locations, and active skills
3. **Active Bonuses**: Perform mini-games or timed activities for 2-5x multipliers
4. **Resource Management**: Process gathered materials through production chains
5. **Progression Choices**: Allocate experience, unlock new areas/methods

### Secondary Loop (30-60 minutes)
1. **Quest Progression**: Complete story-driven objectives
2. **Equipment Upgrades**: Craft or acquire better tools/gear
3. **Area Unlocks**: Access new regions with unique resources
4. **Skill Synergies**: Unlock cross-skill bonuses and combinations

### Long-term Loop (Days/Weeks)
1. **Mastery Systems**: Deep specialization in chosen skills
2. **Prestige Mechanics**: Reset for permanent bonuses
3. **End-game Content**: Challenging activities requiring multiple maxed skills

## Skill System Design

### Gathering Skills
- **Woodcutting**: Trees → Logs → Various wood types
- **Mining**: Rocks → Ores → Gems and metals
- **Fishing**: Waters → Fish → Rare catches
- **Foraging**: Wild areas → Herbs, berries, rare materials
- **Archaeology**: Ancient sites → Artifacts, knowledge, rare components

### Production Skills
- **Smithing**: Ores → Tools, weapons, equipment
- **Crafting**: Various materials → Useful items, decorations
- **Cooking**: Raw ingredients → Food buffs, trade goods
- **Herblore**: Herbs → Potions, enhancement items
- **Enchanting**: Magical components → Enhanced equipment

### Unique Skills (Our Innovation)
- **Exploration**: Discover new areas, hidden resources, lore
- **Trading**: Market manipulation, caravan management
- **Construction**: Build and upgrade personal base/workshop
- **Research**: Unlock new recipes, methods, and bonuses
- **Taming**: Capture and train creatures for various benefits

## Technical Architecture

### Frontend Stack
- **Next.js 15** (App Router) - Modern React framework
- **TypeScript** - Type safety and developer experience
- **Tailwind CSS** - Utility-first styling
- **Framer Motion** - Smooth animations and transitions
- **Zustand** - Lightweight state management
- **React Hook Form** - Form handling
- **Recharts** - Data visualization for progress tracking

### Game Engine Approach
- **DOM-based UI** - Leveraging React's strengths for complex interfaces
- **Canvas for Visuals** - HTML5 Canvas for animations, particle effects
- **Web Workers** - Background calculations for idle progression
- **Local Storage + IndexedDB** - Client-side save system
- **Optional Backend** - Future multiplayer/leaderboards

### Performance Considerations
- **Incremental Calculation** - Only compute what's needed when needed
- **Efficient Rendering** - Virtual scrolling for large lists
- **Optimistic Updates** - Immediate UI feedback
- **Progressive Loading** - Lazy load game content

## Starter Area: Lumbridge Valley

### Tutorial Quest: "The Awakening"
**Duration**: 15-20 minutes of guided gameplay

#### Phase 1: Basic Gathering (5 minutes)
- **Location**: Lumbridge Forest Edge
- **Objective**: Chop 10 Oak Logs
- **Teaches**: Click to gather, idle progression, basic UI navigation
- **Reward**: Basic Hatchet, 50 Woodcutting XP

#### Phase 2: First Production (5 minutes)
- **Location**: Village Workshop
- **Objective**: Craft 5 Wooden Planks, Cook 3 Trout
- **Teaches**: Resource transformation, skill synergies
- **Reward**: Basic Tools, 100 Crafting XP, 75 Cooking XP

#### Phase 3: Active Rewards (5 minutes)
- **Location**: Training Grounds
- **Objective**: Complete timing mini-game for 2x XP bonus
- **Teaches**: Active participation benefits, skill mastery
- **Reward**: 200 bonus XP, unlock Active Bonus system

#### Phase 4: Progression Choice (5 minutes)
- **Location**: Skill Master's Hall
- **Objective**: Choose first specialization path
- **Teaches**: Character building, long-term planning
- **Reward**: Specialization bonus, unlock advanced areas

### Lumbridge Valley Features
- **5 Distinct Zones**: Forest, River, Hills, Village, Ancient Ruins
- **12 Resource Types**: Oak/Willow logs, Copper/Tin ore, Trout/Salmon, Wild herbs, etc.
- **3 Mini-games**: Timing challenges for gathering bonuses
- **Story Elements**: Environmental storytelling, discoverable lore

## Unique Game Features

### Active Reward System
- **Timing Games**: Perfect timing for 2-5x resource multipliers
- **Combo System**: Chain successful actions for escalating bonuses
- **Focus Mode**: Limited-time high-intensity gathering sessions
- **Daily Challenges**: Special objectives with unique rewards

### Dynamic Progression
- **Adaptive Difficulty**: Content scales with player progress
- **Multiple Paths**: Various ways to achieve the same goals
- **Synergy Bonuses**: Cross-skill combinations unlock special benefits
- **Mastery Tracks**: Deep specialization with meaningful choices

### Quality of Life
- **Smart Notifications**: Contextual alerts without spam
- **Progress Visualization**: Beautiful charts and progress bars
- **Offline Optimization**: AI suggests optimal idle setups
- **One-Click Actions**: Streamlined common operations

## Development Phases

### Phase 1: MVP Demo (2-3 weeks)
- [ ] Next.js project setup with TypeScript
- [ ] Basic UI framework and design system
- [ ] Core game loop implementation
- [ ] Tutorial quest (Lumbridge)
- [ ] 3 skills: Woodcutting, Crafting, Cooking
- [ ] Local save system
- [ ] Basic active reward mini-game

### Phase 2: Core Features (3-4 weeks)
- [ ] All 10 planned skills implemented
- [ ] Equipment and progression systems
- [ ] Advanced UI components
- [ ] Multiple areas beyond Lumbridge
- [ ] Enhanced active reward systems
- [ ] Performance optimizations

### Phase 3: Polish & Expansion (2-3 weeks)
- [ ] Advanced animations and effects
- [ ] Comprehensive tutorial improvements
- [ ] Balancing and playtesting
- [ ] Additional content and areas
- [ ] Achievement system
- [ ] Preparation for beta release

## Success Metrics

### Player Engagement
- **Session Length**: Target 15-30 minutes active sessions
- **Return Rate**: 70%+ day-1 retention, 40%+ day-7 retention
- **Progression Satisfaction**: Players reach mid-game content within first week

### Technical Performance
- **Load Time**: <2 seconds initial load
- **Smooth Performance**: 60fps animations, responsive interactions
- **Save Reliability**: Zero save data loss incidents

### Development Velocity
- **Feature Delivery**: New content every 2-3 weeks
- **Bug Resolution**: Critical bugs fixed within 24 hours
- **Community Feedback**: Regular player input integration

## Risk Mitigation

### Technical Risks
- **Performance**: Regular profiling and optimization
- **Save System**: Multiple backup strategies
- **Browser Compatibility**: Progressive enhancement approach

### Design Risks
- **Complexity Creep**: Strict scope management per phase
- **Balance Issues**: Extensive playtesting and data analysis
- **Player Retention**: A/B testing for key mechanics

### Development Risks
- **Scope Management**: Clear phase boundaries and deliverables
- **Quality Assurance**: Automated testing for core systems
- **Timeline Pressure**: Buffer time built into each phase

---

## Next Steps

1. **Review & Refine**: Discuss this plan and adjust based on feedback
2. **Technical Setup**: Initialize Next.js project with chosen tech stack
3. **Design System**: Create UI components and visual style guide
4. **Core Implementation**: Begin with basic game loop and first skill
5. **Iterative Development**: Build, test, and refine in short cycles

This plan balances ambition with practicality, ensuring we can deliver a compelling demo while building toward a full-featured game that stands out in the idle game space.
