import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { GameState, PlayerState, Skill, ExtractionResult, CraftingResult } from '@/types/game';
import { materials } from '@/data/materials';
import { extractionSources } from '@/data/extractionSources';

// Helper function to calculate experience required for next level
const getExperienceToNext = (level: number): number => {
  return Math.floor(100 * Math.pow(1.2, level - 1));
};

// Helper function to calculate skill-based extraction chance
const calculateExtractionChance = (baseChance: number, skillLevel: number, skillRequirement: number): number => {
  if (skillLevel < skillRequirement) return 0;
  
  // Skill bonus: +2% per level above requirement, capped at 95%
  const skillBonus = Math.min(0.4, (skillLevel - skillRequirement) * 0.02);
  return Math.min(0.95, baseChance + skillBonus);
};

// Helper function to generate weapon stats based on materials
const generateWeaponStats = (materialIds: string[]) => {
  let totalValue = 0;
  let hasRare = false;
  let hasLegendary = false;
  
  materialIds.forEach(id => {
    const material = materials[id];
    if (material) {
      totalValue += material.value;
      if (material.rarity === 'rare') hasRare = true;
      if (material.rarity === 'legendary') hasLegendary = true;
    }
  });

  // Determine weapon type based on materials
  const types: ('melee' | 'ranged' | 'magic')[] = ['melee', 'ranged', 'magic'];
  const weaponType = types[Math.floor(Math.random() * types.length)];
  
  // Calculate base stats
  let damage = Math.floor(totalValue / 10) + 5;
  let speed = 5;
  
  // Apply rarity bonuses
  if (hasRare) {
    damage += 3;
    speed += 1;
  }
  if (hasLegendary) {
    damage += 8;
    speed += 2;
  }
  
  // Type-specific adjustments
  switch (weaponType) {
    case 'melee':
      damage += 2;
      break;
    case 'ranged':
      speed += 2;
      break;
    case 'magic':
      damage += 1;
      speed += 1;
      break;
  }
  
  return { type: weaponType, damage, speed };
};

interface GameStore extends GameState {
  // Actions
  startExtraction: (sourceId: string) => void;
  completeExtraction: (sourceId: string) => ExtractionResult | null;
  craftWeapon: (materialIds: string[]) => CraftingResult;
  addExperience: (skillId: string, amount: number) => void;
  addMaterial: (materialId: string, quantity: number) => void;
  removeMaterial: (materialId: string, quantity: number) => boolean;
  equipWeapon: (weapon: any) => void;
  tick: () => void; // Called every second for idle progression
}

const initialPlayerState: PlayerState = {
  id: 'player1',
  name: 'Adventurer',
  currentArea: 'nexus-city',
  skills: {
    extraction: {
      id: 'extraction',
      name: 'Extraction',
      level: 1,
      experience: 0,
      experienceToNext: getExperienceToNext(1)
    },
    synthesis: {
      id: 'synthesis',
      name: 'Synthesis',
      level: 1,
      experience: 0,
      experienceToNext: getExperienceToNext(1)
    },
    resonance: {
      id: 'resonance',
      name: 'Resonance',
      level: 1,
      experience: 0,
      experienceToNext: getExperienceToNext(1)
    },
    commerce: {
      id: 'commerce',
      name: 'Commerce',
      level: 1,
      experience: 0,
      experienceToNext: getExperienceToNext(1)
    },
    exploration: {
      id: 'exploration',
      name: 'Exploration',
      level: 1,
      experience: 0,
      experienceToNext: getExperienceToNext(1)
    }
  },
  inventory: {},
  gold: 100,
  reputation: 0,
  essenceTokens: 0,
  discoveredRecipes: [],
  unlockedAreas: ['nexus-city', 'abandoned-quarry'],
  activeExtractions: [],
  questProgress: {}
};

export const useGameStore = create<GameStore>()(
  persist(
    (set, get) => ({
      player: initialPlayerState,
      currentTime: Date.now(),
      marketPrices: {},
      globalDiscoveries: [],

      startExtraction: (sourceId: string) => {
        const source = extractionSources[sourceId];
        if (!source) return;

        const now = Date.now();
        const endTime = now + (source.extractionTime * 1000);

        set(state => ({
          player: {
            ...state.player,
            activeExtractions: [
              ...state.player.activeExtractions.filter(e => e.sourceId !== sourceId),
              { sourceId, startTime: now, endTime }
            ]
          }
        }));
      },

      completeExtraction: (sourceId: string): ExtractionResult | null => {
        const state = get();
        const source = extractionSources[sourceId];
        const extraction = state.player.activeExtractions.find(e => e.sourceId === sourceId);
        
        if (!source || !extraction || Date.now() < extraction.endTime) {
          return null;
        }

        const skillLevel = state.player.skills.extraction.level;
        const results: { materialId: string; quantity: number }[] = [];
        let totalExperience = 5; // Base XP

        // Roll for each possible material
        source.materials.forEach(materialChance => {
          const chance = calculateExtractionChance(
            materialChance.baseChance,
            skillLevel,
            materialChance.skillRequirement
          );
          
          if (Math.random() < chance) {
            results.push({ materialId: materialChance.materialId, quantity: 1 });
            const material = materials[materialChance.materialId];
            if (material) {
              totalExperience += material.rarity === 'legendary' ? 20 : 
                                material.rarity === 'rare' ? 10 : 3;
            }
          }
        });

        // Update state
        set(state => {
          const newInventory = { ...state.player.inventory };
          results.forEach(result => {
            newInventory[result.materialId] = (newInventory[result.materialId] || 0) + result.quantity;
          });

          return {
            player: {
              ...state.player,
              inventory: newInventory,
              activeExtractions: state.player.activeExtractions.filter(e => e.sourceId !== sourceId)
            }
          };
        });

        // Add experience
        get().addExperience('extraction', totalExperience);

        return {
          materials: results,
          experience: totalExperience,
          time: source.extractionTime
        };
      },

      craftWeapon: (materialIds: string[]): CraftingResult => {
        const state = get();
        
        // Check if player has all materials
        for (const materialId of materialIds) {
          if ((state.player.inventory[materialId] || 0) < 1) {
            return { success: false, outputs: [], experience: 0 };
          }
        }

        // Remove materials from inventory
        materialIds.forEach(materialId => {
          get().removeMaterial(materialId, 1);
        });

        // Generate weapon
        const weaponStats = generateWeaponStats(materialIds);
        const weaponName = `${weaponStats.type.charAt(0).toUpperCase() + weaponStats.type.slice(1)} Weapon`;
        
        const weapon = {
          id: `weapon-${Date.now()}`,
          name: weaponName,
          type: weaponStats.type,
          damage: weaponStats.damage,
          speed: weaponStats.speed,
          materials: materialIds,
          createdAt: Date.now()
        };

        // Add experience based on materials used
        let experience = 10; // Base crafting XP
        materialIds.forEach(materialId => {
          const material = materials[materialId];
          if (material) {
            experience += material.rarity === 'legendary' ? 25 : 
                         material.rarity === 'rare' ? 15 : 5;
          }
        });

        get().addExperience('synthesis', experience);

        return {
          success: true,
          outputs: [{ materialId: weapon.id, quantity: 1 }],
          experience,
          newRecipeDiscovered: undefined // TODO: Implement recipe discovery
        };
      },

      addExperience: (skillId: string, amount: number) => {
        set(state => {
          const skill = state.player.skills[skillId];
          if (!skill) return state;

          let newExp = skill.experience + amount;
          let newLevel = skill.level;
          let expToNext = skill.experienceToNext;

          // Level up check
          while (newExp >= expToNext) {
            newExp -= expToNext;
            newLevel++;
            expToNext = getExperienceToNext(newLevel);
          }

          return {
            player: {
              ...state.player,
              skills: {
                ...state.player.skills,
                [skillId]: {
                  ...skill,
                  level: newLevel,
                  experience: newExp,
                  experienceToNext: expToNext
                }
              }
            }
          };
        });
      },

      addMaterial: (materialId: string, quantity: number) => {
        set(state => ({
          player: {
            ...state.player,
            inventory: {
              ...state.player.inventory,
              [materialId]: (state.player.inventory[materialId] || 0) + quantity
            }
          }
        }));
      },

      removeMaterial: (materialId: string, quantity: number): boolean => {
        const state = get();
        const currentAmount = state.player.inventory[materialId] || 0;
        
        if (currentAmount < quantity) return false;

        set(state => ({
          player: {
            ...state.player,
            inventory: {
              ...state.player.inventory,
              [materialId]: currentAmount - quantity
            }
          }
        }));

        return true;
      },

      equipWeapon: (weapon: any) => {
        set(state => ({
          player: {
            ...state.player,
            currentWeapon: weapon
          }
        }));
      },

      tick: () => {
        set(state => ({
          currentTime: Date.now()
        }));
      }
    }),
    {
      name: 'idlescaper-game-state',
      version: 1
    }
  )
);
