'use client';

import { useState, useEffect } from 'react';
import { useGameStore } from '@/store/gameStore';

export function QuestPanel() {
  const { player, startExtraction, completeExtraction, craftWeapon, equipWeapon } = useGameStore();
  const [tutorialStep, setTutorialStep] = useState(0);

  // Tutorial quest steps
  const tutorialQuest = {
    id: 'first-blade',
    name: 'The First Blade',
    description: 'Learn the basics of material extraction and weapon crafting',
    steps: [
      {
        id: 'extract-materials',
        name: 'Extract Materials',
        description: 'Extract materials from the Shallow Shimmerstone Vein',
        completed: false,
        requirement: 'Have materials in inventory'
      },
      {
        id: 'craft-weapon',
        name: 'Craft a Weapon',
        description: 'Use the Synthesis tab to forge your first weapon',
        completed: false,
        requirement: 'Craft any weapon'
      },
      {
        id: 'equip-weapon',
        name: 'Equip Your Weapon',
        description: 'Equip the weapon you crafted to unlock combat',
        completed: false,
        requirement: 'Have a weapon equipped'
      }
    ]
  };

  // Check tutorial progress
  useEffect(() => {
    const hasMaterials = Object.keys(player.inventory).length > 0;
    const hasWeapon = !!player.currentWeapon;

    if (hasWeapon) {
      setTutorialStep(3); // Complete
    } else if (hasMaterials) {
      setTutorialStep(1); // Can craft
    } else {
      setTutorialStep(0); // Need to extract
    }
  }, [player.inventory, player.currentWeapon]);

  const isTutorialComplete = !!player.currentWeapon;

  const handleQuickExtract = () => {
    startExtraction('shimmerstone-vein-1');
  };

  const handleQuickCraft = () => {
    const availableMaterials = Object.keys(player.inventory).filter(id => 
      player.inventory[id] > 0
    );
    if (availableMaterials.length >= 2) {
      const materialsToUse = availableMaterials.slice(0, 2);
      craftWeapon(materialsToUse);
    }
  };

  const handleQuickEquip = () => {
    const weapon = {
      id: 'tutorial-weapon',
      name: 'First Blade',
      type: 'melee' as const,
      damage: 12,
      speed: 6,
      materials: ['dull-shimmer'],
      createdAt: Date.now()
    };
    equipWeapon(weapon);
  };

  return (
    <div className="space-y-4">
      {/* Active Quest */}
      {!isTutorialComplete && (
        <div className="bg-slate-800 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-3">
            <span className="text-xl">📜</span>
            <h3 className="text-lg font-semibold text-yellow-400">Active Quest</h3>
          </div>
          
          <div className="space-y-3">
            <div>
              <h4 className="font-semibold text-white">{tutorialQuest.name}</h4>
              <p className="text-sm text-slate-300">{tutorialQuest.description}</p>
            </div>

            {/* Quest Steps */}
            <div className="space-y-2">
              {tutorialQuest.steps.map((step, index) => {
                const isActive = index === tutorialStep;
                const isCompleted = index < tutorialStep;
                
                return (
                  <div
                    key={step.id}
                    className={`
                      p-2 rounded border text-sm
                      ${isCompleted 
                        ? 'border-green-600 bg-green-900/20 text-green-300'
                        : isActive
                          ? 'border-yellow-600 bg-yellow-900/20 text-yellow-300'
                          : 'border-slate-600 bg-slate-700 text-slate-400'
                      }
                    `}
                  >
                    <div className="flex items-center space-x-2">
                      <span>
                        {isCompleted ? '✅' : isActive ? '🔄' : '⏳'}
                      </span>
                      <span className="font-medium">{step.name}</span>
                    </div>
                    <p className="text-xs mt-1 ml-6">{step.description}</p>
                  </div>
                );
              })}
            </div>

            {/* Quick Action Button */}
            {tutorialStep === 0 && (
              <button
                onClick={handleQuickExtract}
                className="w-full py-2 bg-orange-600 hover:bg-orange-700 text-white rounded text-sm font-medium"
              >
                Quick Extract (Shimmerstone)
              </button>
            )}
            
            {tutorialStep === 1 && Object.keys(player.inventory).length >= 2 && (
              <button
                onClick={handleQuickCraft}
                className="w-full py-2 bg-purple-600 hover:bg-purple-700 text-white rounded text-sm font-medium"
              >
                Quick Craft Weapon
              </button>
            )}
          </div>
        </div>
      )}

      {/* Quest Complete */}
      {isTutorialComplete && (
        <div className="bg-green-900/20 border border-green-600 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <span className="text-xl">✅</span>
            <h3 className="text-lg font-semibold text-green-400">Quest Complete!</h3>
          </div>
          <p className="text-sm text-green-300">
            You've completed "The First Blade" tutorial quest. Combat areas are now unlocked!
          </p>
        </div>
      )}

      {/* Current Weapon */}
      {player.currentWeapon && (
        <div className="bg-slate-800 rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-3 text-green-400">Current Weapon</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-slate-300">Name:</span>
              <span className="text-white">{player.currentWeapon.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-300">Type:</span>
              <span className="text-white capitalize">{player.currentWeapon.type}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-300">Damage:</span>
              <span className="text-white">{player.currentWeapon.damage}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-300">Speed:</span>
              <span className="text-white">{player.currentWeapon.speed}</span>
            </div>
          </div>
        </div>
      )}

      {/* Resources */}
      <div className="bg-slate-800 rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-3 text-yellow-400">Resources</h3>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-slate-300">Gold:</span>
            <span className="text-yellow-400">{player.gold.toLocaleString()}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-slate-300">Materials:</span>
            <span className="text-white">{Object.keys(player.inventory).length} types</span>
          </div>
          <div className="flex justify-between">
            <span className="text-slate-300">Total Items:</span>
            <span className="text-white">{Object.values(player.inventory).reduce((sum, qty) => sum + qty, 0)}</span>
          </div>
        </div>
      </div>

      {/* Active Extractions */}
      {player.activeExtractions.length > 0 && (
        <div className="bg-slate-800 rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-3 text-blue-400">Active Extractions</h3>
          <div className="space-y-2">
            {player.activeExtractions.map((extraction, index) => {
              const timeLeft = Math.max(0, extraction.endTime - Date.now());
              const isComplete = timeLeft === 0;
              
              return (
                <div key={index} className="text-sm">
                  <div className="flex justify-between items-center">
                    <span className="text-slate-300">Extraction {index + 1}</span>
                    <span className={isComplete ? 'text-green-400' : 'text-yellow-400'}>
                      {isComplete ? 'Complete!' : `${Math.ceil(timeLeft / 1000)}s`}
                    </span>
                  </div>
                  {isComplete && (
                    <button
                      onClick={() => completeExtraction(extraction.sourceId)}
                      className="w-full mt-1 py-1 bg-green-600 hover:bg-green-700 text-white rounded text-xs"
                    >
                      Collect
                    </button>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Tips */}
      <div className="bg-slate-800 rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-3 text-indigo-400">Tips</h3>
        <div className="text-sm text-slate-300 space-y-1">
          <p>• Higher skill levels = better rare chances</p>
          <p>• Experiment with material combinations</p>
          <p>• Each weapon type has unique advantages</p>
        </div>
      </div>
    </div>
  );
}
